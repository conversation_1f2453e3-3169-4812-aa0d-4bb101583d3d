#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化测试脚本 - 测试HTML解析和坐标匹配功能
"""

import os
from html_parser import CaptchaHTMLParser
from coordinate_matcher import CoordinateMatcher


def test_html_parser():
    """测试HTML解析器"""
    print("测试HTML解析器...")
    print("-" * 30)
    
    if not os.path.exists('1.html'):
        print("❌ 找不到1.html文件")
        return False
    
    try:
        parser = CaptchaHTMLParser('1.html')
        image, chars = parser.get_captcha_info()
        
        if image:
            print(f"✅ 成功提取CAPTCHA图像，尺寸: {image.size}")
            parser.save_captcha_image('extracted_captcha.png')
            print("✅ 图像已保存为 extracted_captcha.png")
        else:
            print("❌ 未能提取CAPTCHA图像")
            return False
        
        if chars:
            print(f"✅ 成功提取字符序列: {chars}")
            print(f"原始指令文本: '{parser.instruction_text}'")
        else:
            print("❌ 未能提取字符序列")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ HTML解析器测试失败: {e}")
        return False


def test_coordinate_matcher():
    """测试坐标匹配器"""
    print("\n测试坐标匹配器...")
    print("-" * 30)
    
    try:
        # 模拟OCR识别结果
        required_chars = ['日', '无', '逊', '莽']
        detected_chars = [
            {'text': '日', 'center': (100, 80), 'confidence': 0.95, 'bbox': [[90,70],[110,70],[110,90],[90,90]]},
            {'text': '无', 'center': (150, 80), 'confidence': 0.92, 'bbox': [[140,70],[160,70],[160,90],[140,90]]},
            {'text': '逊', 'center': (200, 80), 'confidence': 0.88, 'bbox': [[190,70],[210,70],[210,90],[190,90]]},
            {'text': '莽', 'center': (250, 80), 'confidence': 0.90, 'bbox': [[240,70],[260,70],[260,90],[240,90]]},
            {'text': '其', 'center': (300, 80), 'confidence': 0.85, 'bbox': [[290,70],[310,70],[310,90],[290,90]]},
            {'text': '他', 'center': (350, 80), 'confidence': 0.87, 'bbox': [[340,70],[360,70],[360,90],[340,90]]},
        ]
        
        matcher = CoordinateMatcher(similarity_threshold=0.8)
        results = matcher.get_detailed_results(required_chars, detected_chars)
        
        print("字符匹配结果:")
        matcher.print_matching_results(results)
        
        coordinates = results['coordinates']
        print("\n点击坐标序列:")
        for i, coord in enumerate(coordinates):
            if coord:
                print(f"  第{i+1}步: 点击字符 '{required_chars[i]}' 坐标 {coord}")
            else:
                print(f"  第{i+1}步: 字符 '{required_chars[i]}' 未找到匹配")
        
        success_rate = results['statistics']['success_rate']
        if success_rate >= 0.8:
            print(f"✅ 坐标匹配测试通过 (成功率: {success_rate:.1%})")
            return True
        else:
            print(f"❌ 坐标匹配成功率过低: {success_rate:.1%}")
            return False
        
    except Exception as e:
        print(f"❌ 坐标匹配器测试失败: {e}")
        return False


def simulate_full_process():
    """模拟完整的处理流程"""
    print("\n模拟完整处理流程...")
    print("-" * 30)
    
    try:
        # 1. 解析HTML
        parser = CaptchaHTMLParser('1.html')
        image, required_chars = parser.get_captcha_info()
        
        if not image or not required_chars:
            print("❌ HTML解析失败")
            return False
        
        print(f"步骤1: 提取到字符序列 {required_chars}")
        
        # 2. 模拟OCR结果（实际应用中这里会调用OCR）
        print("步骤2: 模拟OCR识别结果...")
        
        # 根据实际的CAPTCHA图像尺寸调整坐标
        img_width, img_height = image.size
        print(f"图像尺寸: {img_width} x {img_height}")
        
        # 假设字符在图像中均匀分布
        char_spacing = img_width // (len(required_chars) + 1)
        y_center = img_height // 2
        
        detected_chars = []
        for i, char in enumerate(required_chars):
            x_center = char_spacing * (i + 1)
            detected_chars.append({
                'text': char,
                'center': (x_center, y_center),
                'confidence': 0.90 + i * 0.01,  # 模拟不同的置信度
                'bbox': [[x_center-15, y_center-15], [x_center+15, y_center-15], 
                        [x_center+15, y_center+15], [x_center-15, y_center+15]]
            })
        
        # 添加一些干扰字符
        detected_chars.append({
            'text': '其',
            'center': (img_width - 30, y_center),
            'confidence': 0.85,
            'bbox': [[img_width-45, y_center-15], [img_width-15, y_center-15], 
                    [img_width-15, y_center+15], [img_width-45, y_center+15]]
        })
        
        print(f"模拟识别到 {len(detected_chars)} 个字符")
        
        # 3. 坐标匹配
        print("步骤3: 进行坐标匹配...")
        matcher = CoordinateMatcher(similarity_threshold=0.7)
        results = matcher.get_detailed_results(required_chars, detected_chars)
        
        # 4. 输出结果
        print("\n=== 最终结果 ===")
        print(f"需要点击的字符: {required_chars}")
        print("点击坐标序列:")
        
        coordinates = results['coordinates']
        for i, coord in enumerate(coordinates):
            if coord:
                print(f"  第{i+1}步: 点击字符 '{required_chars[i]}' 坐标 ({coord[0]}, {coord[1]})")
            else:
                print(f"  第{i+1}步: 字符 '{required_chars[i]}' 未找到匹配")
        
        success_rate = results['statistics']['success_rate']
        print(f"\n匹配成功率: {success_rate:.1%}")
        
        if success_rate == 1.0:
            print("🎉 完美匹配！所有字符都找到了对应的坐标")
            return True
        elif success_rate >= 0.8:
            print("✅ 匹配良好，大部分字符都找到了坐标")
            return True
        else:
            print("⚠️ 匹配率较低，可能需要调整参数")
            return False
        
    except Exception as e:
        print(f"❌ 完整流程模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("CAPTCHA解决器简化测试")
    print("=" * 50)
    
    tests = [
        ("HTML解析器", test_html_parser),
        ("坐标匹配器", test_coordinate_matcher),
        ("完整流程模拟", simulate_full_process)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 基础功能测试全部通过!")
        print("\n下一步:")
        print("1. 安装PaddleOCR: pip install paddleocr")
        print("2. 运行完整测试: python captcha_solver.py")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
    
    print(f"\n生成的文件:")
    if os.path.exists('extracted_captcha.png'):
        print("  ✓ extracted_captcha.png - 提取的CAPTCHA图像")


if __name__ == "__main__":
    main()
