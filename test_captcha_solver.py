#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA解决器测试脚本
"""

import os
import sys
import time
import traceback
from captcha_solver import CaptchaSolver


def test_html_parser():
    """测试HTML解析器"""
    print("测试1: HTML解析器")
    print("-" * 30)
    
    try:
        from html_parser import CaptchaHTMLParser
        
        if not os.path.exists('1.html'):
            print("❌ 测试失败: 找不到1.html文件")
            return False
        
        parser = CaptchaHTMLParser('1.html')
        image, chars = parser.get_captcha_info()
        
        if image:
            print(f"✅ 成功提取CAPTCHA图像，尺寸: {image.size}")
            parser.save_captcha_image('test_captcha_image.png')
            print("✅ 图像已保存为 test_captcha_image.png")
        else:
            print("❌ 未能提取CAPTCHA图像")
            return False
        
        if chars:
            print(f"✅ 成功提取字符序列: {chars}")
        else:
            print("❌ 未能提取字符序列")
            return False
        
        print(f"原始指令文本: '{parser.instruction_text}'")
        return True
        
    except Exception as e:
        print(f"❌ HTML解析器测试失败: {e}")
        traceback.print_exc()
        return False


def test_ocr_detector():
    """测试OCR检测器"""
    print("\n测试2: OCR字符检测器")
    print("-" * 30)
    
    try:
        from ocr_detector import OCRCharacterDetector
        from PIL import Image
        
        if not os.path.exists('test_captcha_image.png'):
            print("❌ 测试失败: 找不到test_captcha_image.png文件")
            print("请先运行HTML解析器测试")
            return False
        
        detector = OCRCharacterDetector(use_gpu=False)
        image = Image.open('test_captcha_image.png')
        
        print("正在进行OCR识别...")
        characters = detector.detect_characters(image)
        
        if characters:
            print(f"✅ 成功识别 {len(characters)} 个字符:")
            for i, char in enumerate(characters):
                print(f"  {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                      f"置信度: {char['confidence']:.3f}")
            
            # 保存可视化结果
            detector.visualize_detection(image, characters, 'test_ocr_result.png')
            print("✅ OCR结果可视化已保存为 test_ocr_result.png")
            return True
        else:
            print("❌ OCR未能识别出任何字符")
            return False
        
    except Exception as e:
        print(f"❌ OCR检测器测试失败: {e}")
        traceback.print_exc()
        return False


def test_coordinate_matcher():
    """测试坐标匹配器"""
    print("\n测试3: 坐标匹配器")
    print("-" * 30)
    
    try:
        from coordinate_matcher import CoordinateMatcher
        
        # 模拟测试数据
        required_chars = ['日', '无', '逊', '莽']
        detected_chars = [
            {'text': '日', 'center': (100, 50), 'confidence': 0.95, 'bbox': [[90,40],[110,40],[110,60],[90,60]]},
            {'text': '无', 'center': (200, 50), 'confidence': 0.92, 'bbox': [[190,40],[210,40],[210,60],[190,60]]},
            {'text': '逊', 'center': (300, 50), 'confidence': 0.88, 'bbox': [[290,40],[310,40],[310,60],[290,60]]},
            {'text': '莽', 'center': (400, 50), 'confidence': 0.90, 'bbox': [[390,40],[410,40],[410,60],[390,60]]},
            {'text': '其', 'center': (150, 100), 'confidence': 0.85, 'bbox': [[140,90],[160,90],[160,110],[140,110]]},
        ]
        
        matcher = CoordinateMatcher(similarity_threshold=0.8)
        results = matcher.get_detailed_results(required_chars, detected_chars)
        
        print("匹配结果:")
        matcher.print_matching_results(results)
        
        if results['statistics']['success_rate'] >= 0.8:
            print("✅ 坐标匹配测试通过")
            return True
        else:
            print("❌ 坐标匹配成功率过低")
            return False
        
    except Exception as e:
        print(f"❌ 坐标匹配器测试失败: {e}")
        traceback.print_exc()
        return False


def test_full_solver():
    """测试完整的解决器"""
    print("\n测试4: 完整CAPTCHA解决器")
    print("-" * 30)
    
    try:
        if not os.path.exists('1.html'):
            print("❌ 测试失败: 找不到1.html文件")
            return False
        
        solver = CaptchaSolver(use_gpu=False, similarity_threshold=0.7)
        
        print("正在解决CAPTCHA...")
        start_time = time.time()
        result = solver.solve_captcha('1.html', save_debug_images=True)
        end_time = time.time()
        
        solver.print_solution(result)
        print(f"处理时间: {end_time - start_time:.2f} 秒")
        
        if result['success']:
            print("✅ 完整解决器测试通过")
            
            # 测试简化接口
            print("\n测试简化接口:")
            coordinates = solver.get_click_coordinates_only('1.html')
            print(f"坐标列表: {coordinates}")
            
            return True
        else:
            print("❌ 完整解决器测试失败")
            return False
        
    except Exception as e:
        print(f"❌ 完整解决器测试失败: {e}")
        traceback.print_exc()
        return False


def check_dependencies():
    """检查依赖库"""
    print("检查依赖库...")
    print("-" * 30)
    
    dependencies = [
        ('paddleocr', 'PaddleOCR'),
        ('PIL', 'Pillow'),
        ('cv2', 'OpenCV'),
        ('bs4', 'BeautifulSoup4'),
        ('numpy', 'NumPy')
    ]
    
    missing_deps = []
    
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"✅ {name} - 已安装")
        except ImportError:
            print(f"❌ {name} - 未安装")
            missing_deps.append(name)
    
    if missing_deps:
        print(f"\n缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖库都已安装")
    return True


def main():
    """主测试函数"""
    print("CAPTCHA解决器测试套件")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装所需依赖")
        return
    
    # 运行测试
    tests = [
        ("HTML解析器", test_html_parser),
        ("OCR检测器", test_ocr_detector),
        ("坐标匹配器", test_coordinate_matcher),
        ("完整解决器", test_full_solver)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试都通过了!")
        print("\n使用说明:")
        print("1. 将CAPTCHA HTML文件命名为 '1.html'")
        print("2. 运行: python captcha_solver.py")
        print("3. 查看生成的调试图像和坐标结果")
    else:
        print("⚠️  部分测试失败，请检查错误信息")
    
    print("\n生成的文件:")
    files = [
        'test_captcha_image.png',
        'test_ocr_result.png',
        'debug_captcha_original.png',
        'debug_ocr_result.png'
    ]
    
    for file in files:
        if os.path.exists(file):
            print(f"  ✓ {file}")


if __name__ == "__main__":
    main()
