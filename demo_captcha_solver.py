#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA验证码解决器演示版本
使用模拟OCR数据演示完整功能，无需安装PaddleOCR
"""

import os
import time
from typing import List, Tuple, Dict
from PIL import Image, ImageDraw, ImageFont

from html_parser import CaptchaHTMLParser
from coordinate_matcher import CoordinateMatcher


class MockOCRDetector:
    """模拟OCR检测器"""
    
    def __init__(self):
        """初始化模拟OCR检测器"""
        print("使用模拟OCR检测器（演示模式）")
    
    def detect_characters(self, image: Image.Image) -> List[Dict]:
        """
        模拟OCR字符检测
        
        Args:
            image: PIL图像对象
            
        Returns:
            List[Dict]: 模拟的字符检测结果
        """
        # 获取图像尺寸
        width, height = image.size
        
        # 模拟常见的CAPTCHA字符
        common_chars = ['日', '无', '逊', '莽', '其', '他', '字', '符', '验', '证']
        
        # 生成模拟的字符检测结果
        characters = []
        
        # 假设字符在图像中大致均匀分布
        num_chars = 6  # 模拟检测到6个字符
        char_width = width // num_chars
        y_center = height // 2
        
        for i in range(num_chars):
            x_center = char_width // 2 + i * char_width
            
            # 添加一些随机偏移
            x_offset = (i % 3 - 1) * 5  # -5, 0, 5的偏移
            y_offset = (i % 2) * 10 - 5  # -5或5的偏移
            
            char_x = x_center + x_offset
            char_y = y_center + y_offset
            
            # 选择字符
            char_text = common_chars[i % len(common_chars)]
            
            # 生成边界框
            bbox_size = 20
            bbox = [
                [char_x - bbox_size//2, char_y - bbox_size//2],
                [char_x + bbox_size//2, char_y - bbox_size//2],
                [char_x + bbox_size//2, char_y + bbox_size//2],
                [char_x - bbox_size//2, char_y + bbox_size//2]
            ]
            
            # 生成置信度（模拟真实OCR的不确定性）
            confidence = 0.85 + (i % 3) * 0.05  # 0.85-0.95之间
            
            characters.append({
                'text': char_text,
                'bbox': bbox,
                'confidence': confidence,
                'center': (char_x, char_y)
            })
        
        return characters
    
    def visualize_detection(self, image: Image.Image, characters: List[Dict], 
                          output_path: str = None) -> Image.Image:
        """
        可视化检测结果
        """
        # 创建图像副本
        result_image = image.copy()
        draw = ImageDraw.Draw(result_image)
        
        # 尝试使用系统字体
        try:
            font = ImageFont.truetype("arial.ttf", 12)
        except:
            font = ImageFont.load_default()
        
        # 绘制检测结果
        for char in characters:
            bbox = char['bbox']
            center = char['center']
            text = char['text']
            confidence = char['confidence']
            
            # 绘制边界框
            bbox_flat = [coord for point in bbox for coord in point]
            draw.polygon(bbox_flat, outline='green', width=2)
            
            # 绘制中心点
            draw.ellipse([center[0]-3, center[1]-3, center[0]+3, center[1]+3], 
                        fill='red', outline='red')
            
            # 添加文本标签
            label = f"{text}({confidence:.2f})"
            draw.text((center[0]-15, center[1]-25), label, fill='blue', font=font)
        
        # 保存图像
        if output_path:
            result_image.save(output_path)
            print(f"可视化结果已保存到: {output_path}")
        
        return result_image


class DemoCaptchaSolver:
    """演示版CAPTCHA解决器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化演示解决器
        
        Args:
            similarity_threshold: 字符匹配相似度阈值
        """
        self.html_parser = None
        self.ocr_detector = MockOCRDetector()
        self.coordinate_matcher = CoordinateMatcher(similarity_threshold=similarity_threshold)
        
        print("演示版CAPTCHA解决器初始化完成")
    
    def solve_captcha(self, html_file_path: str, 
                     save_debug_images: bool = True) -> Dict:
        """
        解决CAPTCHA验证码（演示版）
        
        Args:
            html_file_path: HTML文件路径
            save_debug_images: 是否保存调试图像
            
        Returns:
            Dict: 解决结果
        """
        print(f"开始处理CAPTCHA: {html_file_path}")
        
        # 1. 解析HTML文件
        print("步骤1: 解析HTML文件...")
        self.html_parser = CaptchaHTMLParser(html_file_path)
        captcha_image, required_chars = self.html_parser.get_captcha_info()
        
        if not captcha_image:
            return {
                'success': False,
                'error': '无法从HTML文件中提取CAPTCHA图像',
                'coordinates': [],
                'details': {}
            }
        
        if not required_chars:
            return {
                'success': False,
                'error': '无法从HTML文件中提取字符指令',
                'coordinates': [],
                'details': {}
            }
        
        print(f"  ✓ 成功提取CAPTCHA图像 (尺寸: {captcha_image.size})")
        print(f"  ✓ 需要点击的字符序列: {required_chars}")
        
        # 保存原始图像
        if save_debug_images:
            captcha_image.save('demo_captcha_original.png')
            print("  ✓ 原始图像已保存: demo_captcha_original.png")
        
        # 2. 模拟OCR字符识别
        print("步骤2: 模拟OCR字符识别...")
        
        # 为了演示效果，我们确保模拟的OCR结果包含所需的字符
        detected_chars = self._generate_realistic_ocr_results(captcha_image, required_chars)
        
        print(f"  ✓ 模拟识别到 {len(detected_chars)} 个字符")
        for i, char in enumerate(detected_chars):
            print(f"    {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")
        
        # 保存OCR可视化结果
        if save_debug_images:
            self.ocr_detector.visualize_detection(
                captcha_image, detected_chars, 'demo_ocr_result.png'
            )
            print("  ✓ OCR结果可视化已保存: demo_ocr_result.png")
        
        # 3. 坐标匹配
        print("步骤3: 字符坐标匹配...")
        matching_results = self.coordinate_matcher.get_detailed_results(
            required_chars, detected_chars
        )
        
        coordinates = matching_results['coordinates']
        success_rate = matching_results['statistics']['success_rate']
        
        print(f"  ✓ 匹配成功率: {success_rate:.1%}")
        
        # 4. 生成结果
        success = success_rate >= 0.8
        
        result = {
            'success': success,
            'coordinates': coordinates,
            'required_chars': required_chars,
            'click_sequence': [],
            'details': matching_results,
            'statistics': {
                'total_chars': len(required_chars),
                'matched_chars': matching_results['statistics']['matched_count'],
                'success_rate': success_rate
            }
        }
        
        # 生成点击序列
        for i, (char, coord) in enumerate(zip(required_chars, coordinates)):
            if coord:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': coord,
                    'x': coord[0],
                    'y': coord[1]
                })
            else:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': None,
                    'error': '未找到匹配字符'
                })
        
        return result
    
    def _generate_realistic_ocr_results(self, image: Image.Image, 
                                      required_chars: List[str]) -> List[Dict]:
        """
        生成更真实的OCR模拟结果
        
        Args:
            image: CAPTCHA图像
            required_chars: 需要的字符列表
            
        Returns:
            List[Dict]: 模拟的OCR结果
        """
        width, height = image.size
        characters = []
        
        # 确保所需字符都在结果中
        char_spacing = width // (len(required_chars) + 1)
        y_center = height // 2
        
        for i, char in enumerate(required_chars):
            x_center = char_spacing * (i + 1)
            
            # 添加一些真实的位置偏移
            x_offset = (i % 3 - 1) * 8
            y_offset = (i % 2) * 6 - 3
            
            char_x = x_center + x_offset
            char_y = y_center + y_offset
            
            bbox_size = 18
            bbox = [
                [char_x - bbox_size//2, char_y - bbox_size//2],
                [char_x + bbox_size//2, char_y - bbox_size//2],
                [char_x + bbox_size//2, char_y + bbox_size//2],
                [char_x - bbox_size//2, char_y + bbox_size//2]
            ]
            
            confidence = 0.88 + i * 0.02  # 递增的置信度
            
            characters.append({
                'text': char,
                'bbox': bbox,
                'confidence': confidence,
                'center': (char_x, char_y)
            })
        
        # 添加一些干扰字符
        interference_chars = ['其', '他', '字', '符']
        for i, char in enumerate(interference_chars[:2]):  # 只添加2个干扰字符
            x_center = width - 40 - i * 25
            y_center = height // 2 + (i % 2) * 20 - 10
            
            bbox_size = 16
            bbox = [
                [x_center - bbox_size//2, y_center - bbox_size//2],
                [x_center + bbox_size//2, y_center - bbox_size//2],
                [x_center + bbox_size//2, y_center + bbox_size//2],
                [x_center - bbox_size//2, y_center + bbox_size//2]
            ]
            
            characters.append({
                'text': char,
                'bbox': bbox,
                'confidence': 0.82 + i * 0.03,
                'center': (x_center, y_center)
            })
        
        return characters
    
    def print_solution(self, result: Dict):
        """打印解决方案"""
        print("\n" + "="*50)
        print("CAPTCHA解决方案（演示版）")
        print("="*50)
        
        if result['success']:
            print("✅ 解决成功!")
            print(f"需要点击的字符序列: {result['required_chars']}")
            print("\n点击步骤:")
            
            for step in result['click_sequence']:
                if step.get('coordinate'):
                    print(f"  第{step['step']}步: 点击字符 '{step['character']}' "
                          f"坐标 ({step['x']}, {step['y']})")
                else:
                    print(f"  第{step['step']}步: 字符 '{step['character']}' - "
                          f"{step.get('error', '未知错误')}")
            
            print(f"\n统计信息:")
            stats = result['statistics']
            print(f"  总字符数: {stats['total_chars']}")
            print(f"  匹配成功: {stats['matched_chars']}")
            print(f"  成功率: {stats['success_rate']:.1%}")
            
        else:
            print("❌ 解决失败!")
            if 'error' in result:
                print(f"错误信息: {result['error']}")
        
        print("="*50)


def main():
    """主函数"""
    print("CAPTCHA验证码自动解决器 - 演示版")
    print("使用模拟OCR数据演示完整功能")
    print("-" * 40)
    
    # 检查HTML文件
    html_file = '1.html'
    if not os.path.exists(html_file):
        print(f"错误: 找不到HTML文件 '{html_file}'")
        print("请确保HTML文件存在于当前目录")
        return
    
    try:
        # 创建演示解决器
        solver = DemoCaptchaSolver(similarity_threshold=0.7)
        
        # 解决CAPTCHA
        start_time = time.time()
        result = solver.solve_captcha(html_file, save_debug_images=True)
        end_time = time.time()
        
        # 打印结果
        solver.print_solution(result)
        
        print(f"\n处理时间: {end_time - start_time:.2f} 秒")
        
        # 如果成功，提供使用示例
        if result['success']:
            print("\n🎉 演示成功！")
            print("\n实际使用时的代码示例:")
            print("```python")
            print("# 安装真实的OCR库")
            print("# pip install paddleocr")
            print("")
            print("from captcha_solver import CaptchaSolver")
            print("solver = CaptchaSolver()")
            print("result = solver.solve_captcha('1.html')")
            print("coordinates = result['coordinates']")
            print("")
            print("# 执行点击操作")
            print("import pyautogui")
            print("for coord in coordinates:")
            print("    if coord:")
            print("        pyautogui.click(coord[0], coord[1])")
            print("        time.sleep(0.5)")
            print("```")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
