#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML解析器 - 用于提取CAPTCHA验证码图像和指令文本
"""

import re
import base64
from bs4 import BeautifulSoup
from typing import Tuple, List, Optional
import io
from PIL import Image


class CaptchaHTMLParser:
    """CAPTCHA HTML解析器"""
    
    def __init__(self, html_file_path: str):
        """
        初始化解析器
        
        Args:
            html_file_path: HTML文件路径
        """
        self.html_file_path = html_file_path
        self.soup = None
        self.captcha_image = None
        self.instruction_text = ""
        self.required_chars = []
        
    def parse_html(self) -> bool:
        """
        解析HTML文件
        
        Returns:
            bool: 解析是否成功
        """
        try:
            with open(self.html_file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self.soup = BeautifulSoup(html_content, 'html.parser')
            return True
        except Exception as e:
            print(f"解析HTML文件失败: {e}")
            return False
    
    def extract_captcha_image(self) -> Optional[Image.Image]:
        """
        提取CAPTCHA图像
        
        Returns:
            PIL.Image: CAPTCHA图像对象，如果提取失败返回None
        """
        try:
            # 查找包含base64图像数据的img标签
            img_tags = self.soup.find_all('img')
            
            for img in img_tags:
                src = img.get('src', '')
                if src.startswith('data:image/'):
                    # 提取base64数据
                    base64_data = src.split(',')[1]
                    image_data = base64.b64decode(base64_data)
                    
                    # 转换为PIL图像
                    self.captcha_image = Image.open(io.BytesIO(image_data))
                    return self.captcha_image
            
            print("未找到CAPTCHA图像")
            return None
            
        except Exception as e:
            print(f"提取CAPTCHA图像失败: {e}")
            return None
    
    def extract_instruction_text(self) -> str:
        """
        提取指令文本
        
        Returns:
            str: 指令文本
        """
        try:
            # 查找包含指令的文本
            # 通常指令文本包含"请依次点击"或类似的文字
            text_content = self.soup.get_text()
            
            # 使用正则表达式匹配指令文本
            patterns = [
                r'请依次点击【([^】]+)】',
                r'请按顺序点击[：:]\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)',
                r'请点击[：:]?\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)',
                r'按顺序点击[：:]?\s*([^，,\s]+(?:[，,]\s*[^，,\s]+)*)'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, text_content)
                if match:
                    self.instruction_text = match.group(1)
                    break
            
            if not self.instruction_text:
                # 如果没有找到标准格式，尝试查找包含中文字符的序列
                chinese_char_pattern = r'([一-龯]+(?:[，,]\s*[一-龯]+)*)'
                matches = re.findall(chinese_char_pattern, text_content)
                if matches:
                    # 选择最可能的指令文本（通常是较短的字符序列）
                    self.instruction_text = min(matches, key=len)
            
            return self.instruction_text
            
        except Exception as e:
            print(f"提取指令文本失败: {e}")
            return ""
    
    def parse_required_characters(self) -> List[str]:
        """
        解析需要点击的字符序列
        
        Returns:
            List[str]: 需要点击的字符列表
        """
        if not self.instruction_text:
            return []
        
        try:
            # 分割字符串，支持逗号、中文逗号等分隔符
            chars = re.split(r'[，,、\s]+', self.instruction_text.strip())
            # 过滤空字符串并确保每个元素都是单个字符
            self.required_chars = [char.strip() for char in chars if char.strip()]
            
            return self.required_chars
            
        except Exception as e:
            print(f"解析字符序列失败: {e}")
            return []
    
    def get_captcha_info(self) -> Tuple[Optional[Image.Image], List[str]]:
        """
        获取完整的CAPTCHA信息
        
        Returns:
            Tuple[PIL.Image, List[str]]: (CAPTCHA图像, 需要点击的字符列表)
        """
        if not self.parse_html():
            return None, []
        
        image = self.extract_captcha_image()
        self.extract_instruction_text()
        chars = self.parse_required_characters()
        
        return image, chars
    
    def save_captcha_image(self, output_path: str) -> bool:
        """
        保存CAPTCHA图像到文件
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            bool: 保存是否成功
        """
        try:
            if self.captcha_image:
                self.captcha_image.save(output_path)
                return True
            return False
        except Exception as e:
            print(f"保存图像失败: {e}")
            return False


def main():
    """测试函数"""
    parser = CaptchaHTMLParser('1.html')
    
    # 获取CAPTCHA信息
    image, chars = parser.get_captcha_info()
    
    if image:
        print(f"成功提取CAPTCHA图像，尺寸: {image.size}")
        # 保存图像用于测试
        parser.save_captcha_image('captcha_image.png')
        print("CAPTCHA图像已保存为 captcha_image.png")
    else:
        print("未能提取CAPTCHA图像")
    
    if chars:
        print(f"需要点击的字符序列: {chars}")
    else:
        print("未能提取字符序列")
    
    print(f"原始指令文本: '{parser.instruction_text}'")


if __name__ == "__main__":
    main()
