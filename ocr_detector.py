#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OCR字符检测器 - 使用PaddleOCR识别CAPTCHA图像中的字符及其坐标
"""

import numpy as np
from PIL import Image
from typing import List, Tuple, Dict, Optional
import cv2


class OCRCharacterDetector:
    """OCR字符检测器"""
    
    def __init__(self, use_gpu: bool = False, lang: str = 'ch'):
        """
        初始化OCR检测器
        
        Args:
            use_gpu: 是否使用GPU加速
            lang: 语言设置，'ch'表示中文
        """
        self.use_gpu = use_gpu
        self.lang = lang
        self.ocr = None
        self._init_ocr()
    
    def _init_ocr(self):
        """初始化PaddleOCR"""
        try:
            from paddleocr import PaddleOCR
            self.ocr = PaddleOCR(
                use_angle_cls=True, 
                lang=self.lang,
                use_gpu=self.use_gpu,
                show_log=False
            )
            print("PaddleOCR初始化成功")
        except ImportError:
            print("错误: 请安装PaddleOCR库")
            print("安装命令: pip install paddleocr")
            raise
        except Exception as e:
            print(f"PaddleOCR初始化失败: {e}")
            raise
    
    def preprocess_image(self, image: Image.Image) -> np.ndarray:
        """
        预处理图像以提高OCR识别准确率
        
        Args:
            image: PIL图像对象
            
        Returns:
            np.ndarray: 预处理后的图像数组
        """
        # 转换为numpy数组
        img_array = np.array(image)
        
        # 如果是RGBA，转换为RGB
        if img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
        
        # 转换为BGR格式（OpenCV格式）
        img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        # 图像增强
        # 1. 调整对比度和亮度
        alpha = 1.2  # 对比度控制
        beta = 10    # 亮度控制
        img_enhanced = cv2.convertScaleAbs(img_bgr, alpha=alpha, beta=beta)
        
        # 2. 高斯模糊去噪
        img_blurred = cv2.GaussianBlur(img_enhanced, (3, 3), 0)
        
        # 3. 锐化
        kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        img_sharpened = cv2.filter2D(img_blurred, -1, kernel)
        
        return img_sharpened
    
    def detect_characters(self, image: Image.Image) -> List[Dict]:
        """
        检测图像中的字符及其位置
        
        Args:
            image: PIL图像对象
            
        Returns:
            List[Dict]: 检测到的字符信息列表，每个字典包含:
                - text: 字符文本
                - bbox: 边界框坐标 [[x1,y1], [x2,y2], [x3,y3], [x4,y4]]
                - confidence: 置信度
                - center: 中心点坐标 (x, y)
        """
        if not self.ocr:
            raise RuntimeError("OCR未正确初始化")
        
        try:
            # 预处理图像
            processed_img = self.preprocess_image(image)
            
            # 执行OCR识别
            results = self.ocr.ocr(processed_img, cls=True)
            
            characters = []
            
            if results and results[0]:
                for line in results[0]:
                    if len(line) >= 2:
                        bbox = line[0]  # 边界框坐标
                        text_info = line[1]  # (文本, 置信度)
                        
                        if len(text_info) >= 2:
                            text = text_info[0]
                            confidence = text_info[1]
                            
                            # 计算中心点坐标
                            bbox_array = np.array(bbox)
                            center_x = int(np.mean(bbox_array[:, 0]))
                            center_y = int(np.mean(bbox_array[:, 1]))
                            
                            # 分割单个字符（如果识别结果包含多个字符）
                            if len(text) > 1:
                                # 估算每个字符的位置
                                char_width = (bbox_array[1][0] - bbox_array[0][0]) / len(text)
                                for i, char in enumerate(text):
                                    char_center_x = int(bbox_array[0][0] + char_width * (i + 0.5))
                                    characters.append({
                                        'text': char,
                                        'bbox': bbox,
                                        'confidence': confidence,
                                        'center': (char_center_x, center_y)
                                    })
                            else:
                                characters.append({
                                    'text': text,
                                    'bbox': bbox,
                                    'confidence': confidence,
                                    'center': (center_x, center_y)
                                })
            
            return characters
            
        except Exception as e:
            print(f"OCR识别失败: {e}")
            return []
    
    def filter_characters(self, characters: List[Dict], min_confidence: float = 0.5) -> List[Dict]:
        """
        过滤低置信度的字符
        
        Args:
            characters: 字符检测结果
            min_confidence: 最小置信度阈值
            
        Returns:
            List[Dict]: 过滤后的字符列表
        """
        return [char for char in characters if char['confidence'] >= min_confidence]
    
    def visualize_detection(self, image: Image.Image, characters: List[Dict], 
                          output_path: str = None) -> Image.Image:
        """
        可视化检测结果
        
        Args:
            image: 原始图像
            characters: 检测到的字符
            output_path: 输出文件路径（可选）
            
        Returns:
            PIL.Image: 标注后的图像
        """
        # 转换为OpenCV格式
        img_array = np.array(image)
        if len(img_array.shape) == 3 and img_array.shape[2] == 4:
            img_array = cv2.cvtColor(img_array, cv2.COLOR_RGBA2RGB)
        img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
        
        # 绘制检测结果
        for char in characters:
            bbox = np.array(char['bbox'], dtype=np.int32)
            center = char['center']
            text = char['text']
            confidence = char['confidence']
            
            # 绘制边界框
            cv2.polylines(img_cv, [bbox], True, (0, 255, 0), 2)
            
            # 绘制中心点
            cv2.circle(img_cv, center, 5, (255, 0, 0), -1)
            
            # 添加文本标签
            label = f"{text}({confidence:.2f})"
            cv2.putText(img_cv, label, (center[0]-20, center[1]-10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
        # 转换回PIL格式
        img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
        result_image = Image.fromarray(img_rgb)
        
        # 保存图像
        if output_path:
            result_image.save(output_path)
            print(f"可视化结果已保存到: {output_path}")
        
        return result_image


def main():
    """测试函数"""
    # 测试OCR检测器
    detector = OCRCharacterDetector()
    
    # 加载测试图像
    try:
        image = Image.open('captcha_image.png')
        print(f"加载图像成功，尺寸: {image.size}")
        
        # 检测字符
        characters = detector.detect_characters(image)
        print(f"检测到 {len(characters)} 个字符:")
        
        for i, char in enumerate(characters):
            print(f"  {i+1}. 字符: '{char['text']}', "
                  f"中心坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")
        
        # 可视化结果
        detector.visualize_detection(image, characters, 'ocr_result.png')
        
    except FileNotFoundError:
        print("请先运行html_parser.py生成captcha_image.png文件")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
