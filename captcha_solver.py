#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAPTCHA验证码解决器 - 主程序
自动解析HTML页面中的CAPTCHA验证码，识别字符并返回点击坐标
"""

import os
import sys
import time
from typing import List, Tuple, Optional, Dict
from PIL import Image

from html_parser import CaptchaHTMLParser
from ocr_detector import OCRCharacterDetector
from coordinate_matcher import CoordinateMatcher


class CaptchaSolver:
    """CAPTCHA验证码解决器"""
    
    def __init__(self, use_gpu: bool = False, similarity_threshold: float = 0.8):
        """
        初始化CAPTCHA解决器
        
        Args:
            use_gpu: 是否使用GPU加速OCR
            similarity_threshold: 字符匹配相似度阈值
        """
        self.html_parser = None
        self.ocr_detector = OCRCharacterDetector(use_gpu=use_gpu)
        self.coordinate_matcher = CoordinateMatcher(similarity_threshold=similarity_threshold)
        
        print("CAPTCHA解决器初始化完成")
    
    def solve_captcha(self, html_file_path: str, 
                     save_debug_images: bool = True) -> Dict:
        """
        解决CAPTCHA验证码
        
        Args:
            html_file_path: HTML文件路径
            save_debug_images: 是否保存调试图像
            
        Returns:
            Dict: 解决结果，包含坐标列表和详细信息
        """
        print(f"开始处理CAPTCHA: {html_file_path}")
        
        # 1. 解析HTML文件
        print("步骤1: 解析HTML文件...")
        self.html_parser = CaptchaHTMLParser(html_file_path)
        captcha_image, required_chars = self.html_parser.get_captcha_info()
        
        if not captcha_image:
            return {
                'success': False,
                'error': '无法从HTML文件中提取CAPTCHA图像',
                'coordinates': [],
                'details': {}
            }
        
        if not required_chars:
            return {
                'success': False,
                'error': '无法从HTML文件中提取字符指令',
                'coordinates': [],
                'details': {}
            }
        
        print(f"  ✓ 成功提取CAPTCHA图像 (尺寸: {captcha_image.size})")
        print(f"  ✓ 需要点击的字符序列: {required_chars}")
        
        # 保存原始图像
        if save_debug_images:
            captcha_image.save('debug_captcha_original.png')
            print("  ✓ 原始图像已保存: debug_captcha_original.png")
        
        # 2. OCR字符识别
        print("步骤2: OCR字符识别...")
        detected_chars = self.ocr_detector.detect_characters(captcha_image)
        
        if not detected_chars:
            return {
                'success': False,
                'error': 'OCR未能识别出任何字符',
                'coordinates': [],
                'details': {
                    'required_chars': required_chars,
                    'detected_chars': []
                }
            }
        
        print(f"  ✓ 识别到 {len(detected_chars)} 个字符")
        for i, char in enumerate(detected_chars):
            print(f"    {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")
        
        # 保存OCR可视化结果
        if save_debug_images:
            self.ocr_detector.visualize_detection(
                captcha_image, detected_chars, 'debug_ocr_result.png'
            )
            print("  ✓ OCR结果可视化已保存: debug_ocr_result.png")
        
        # 3. 坐标匹配
        print("步骤3: 字符坐标匹配...")
        matching_results = self.coordinate_matcher.get_detailed_results(
            required_chars, detected_chars
        )
        
        coordinates = matching_results['coordinates']
        success_rate = matching_results['statistics']['success_rate']
        
        print(f"  ✓ 匹配成功率: {success_rate:.1%}")
        
        # 4. 生成结果
        success = success_rate >= 0.8  # 80%以上匹配率认为成功
        
        result = {
            'success': success,
            'coordinates': coordinates,
            'required_chars': required_chars,
            'click_sequence': [],
            'details': matching_results,
            'statistics': {
                'total_chars': len(required_chars),
                'matched_chars': matching_results['statistics']['matched_count'],
                'success_rate': success_rate
            }
        }
        
        # 生成点击序列
        for i, (char, coord) in enumerate(zip(required_chars, coordinates)):
            if coord:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': coord,
                    'x': coord[0],
                    'y': coord[1]
                })
            else:
                result['click_sequence'].append({
                    'step': i + 1,
                    'character': char,
                    'coordinate': None,
                    'error': '未找到匹配字符'
                })
        
        return result
    
    def print_solution(self, result: Dict):
        """
        打印解决方案
        
        Args:
            result: solve_captcha返回的结果
        """
        print("\n" + "="*50)
        print("CAPTCHA解决方案")
        print("="*50)
        
        if result['success']:
            print("✅ 解决成功!")
            print(f"需要点击的字符序列: {result['required_chars']}")
            print("\n点击步骤:")
            
            for step in result['click_sequence']:
                if step.get('coordinate'):
                    print(f"  第{step['step']}步: 点击字符 '{step['character']}' "
                          f"坐标 ({step['x']}, {step['y']})")
                else:
                    print(f"  第{step['step']}步: 字符 '{step['character']}' - "
                          f"{step.get('error', '未知错误')}")
            
            print(f"\n统计信息:")
            stats = result['statistics']
            print(f"  总字符数: {stats['total_chars']}")
            print(f"  匹配成功: {stats['matched_chars']}")
            print(f"  成功率: {stats['success_rate']:.1%}")
            
        else:
            print("❌ 解决失败!")
            if 'error' in result:
                print(f"错误信息: {result['error']}")
        
        print("="*50)
    
    def get_click_coordinates_only(self, html_file_path: str) -> List[Tuple[int, int]]:
        """
        简化接口：只返回点击坐标列表
        
        Args:
            html_file_path: HTML文件路径
            
        Returns:
            List[Tuple[int, int]]: 点击坐标列表，失败的位置为None
        """
        result = self.solve_captcha(html_file_path, save_debug_images=False)
        return result.get('coordinates', [])


def main():
    """主函数"""
    print("CAPTCHA验证码自动解决器")
    print("支持中文字符识别和坐标定位")
    print("-" * 40)
    
    # 检查HTML文件
    html_file = '1.html'
    if not os.path.exists(html_file):
        print(f"错误: 找不到HTML文件 '{html_file}'")
        print("请确保HTML文件存在于当前目录")
        return
    
    try:
        # 创建解决器
        solver = CaptchaSolver(use_gpu=False, similarity_threshold=0.7)
        
        # 解决CAPTCHA
        start_time = time.time()
        result = solver.solve_captcha(html_file, save_debug_images=True)
        end_time = time.time()
        
        # 打印结果
        solver.print_solution(result)
        
        print(f"\n处理时间: {end_time - start_time:.2f} 秒")
        
        # 如果成功，提供使用示例
        if result['success']:
            print("\n使用示例:")
            print("# 获取点击坐标")
            print("coordinates = solver.get_click_coordinates_only('1.html')")
            print("# 执行点击操作")
            print("for i, coord in enumerate(coordinates):")
            print("    if coord:")
            print("        print(f'点击第{i+1}个字符，坐标: {coord}')")
            print("        # 这里可以添加实际的点击操作代码")
            print("        # 例如使用selenium、pyautogui等库")
        
    except Exception as e:
        print(f"程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
