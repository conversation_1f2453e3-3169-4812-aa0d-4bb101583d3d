#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标匹配器 - 将指令中的字符与OCR识别的字符进行匹配，确定点击坐标
"""

from typing import List, Dict, Tuple, Optional
import difflib
from collections import defaultdict


class CoordinateMatcher:
    """坐标匹配器"""
    
    def __init__(self, similarity_threshold: float = 0.8):
        """
        初始化坐标匹配器
        
        Args:
            similarity_threshold: 字符相似度阈值
        """
        self.similarity_threshold = similarity_threshold
    
    def calculate_similarity(self, char1: str, char2: str) -> float:
        """
        计算两个字符的相似度
        
        Args:
            char1: 字符1
            char2: 字符2
            
        Returns:
            float: 相似度分数 (0-1)
        """
        if char1 == char2:
            return 1.0
        
        # 使用difflib计算相似度
        similarity = difflib.SequenceMatcher(None, char1, char2).ratio()
        return similarity
    
    def find_character_matches(self, required_chars: List[str], 
                             detected_chars: List[Dict]) -> Dict[str, List[Dict]]:
        """
        为每个需要的字符找到可能的匹配
        
        Args:
            required_chars: 需要点击的字符列表
            detected_chars: OCR检测到的字符列表
            
        Returns:
            Dict[str, List[Dict]]: 每个需要字符对应的可能匹配列表
        """
        matches = defaultdict(list)
        
        for required_char in required_chars:
            for detected_char in detected_chars:
                similarity = self.calculate_similarity(required_char, detected_char['text'])
                
                if similarity >= self.similarity_threshold:
                    match_info = detected_char.copy()
                    match_info['similarity'] = similarity
                    matches[required_char].append(match_info)
        
        # 按相似度排序
        for char in matches:
            matches[char].sort(key=lambda x: x['similarity'], reverse=True)
        
        return dict(matches)
    
    def resolve_conflicts(self, matches: Dict[str, List[Dict]]) -> Dict[str, Dict]:
        """
        解决字符匹配冲突（多个需要字符匹配到同一个检测字符）
        
        Args:
            matches: 字符匹配结果
            
        Returns:
            Dict[str, Dict]: 解决冲突后的最终匹配结果
        """
        # 记录每个检测字符被匹配的情况
        detected_char_usage = defaultdict(list)
        
        # 收集所有匹配关系
        for required_char, match_list in matches.items():
            for match in match_list:
                detected_char_usage[match['text']].append({
                    'required_char': required_char,
                    'match_info': match
                })
        
        final_matches = {}
        used_detected_chars = set()
        
        # 优先处理唯一匹配
        for required_char, match_list in matches.items():
            if not match_list:
                continue
                
            best_match = match_list[0]
            detected_text = best_match['text']
            
            # 如果这个检测字符只被一个需要字符匹配，直接分配
            if len(detected_char_usage[detected_text]) == 1:
                final_matches[required_char] = best_match
                used_detected_chars.add(detected_text)
        
        # 处理冲突情况
        for detected_text, usage_list in detected_char_usage.items():
            if detected_text in used_detected_chars:
                continue
                
            if len(usage_list) > 1:
                # 多个需要字符匹配到同一个检测字符，选择相似度最高的
                best_usage = max(usage_list, key=lambda x: x['match_info']['similarity'])
                required_char = best_usage['required_char']
                
                if required_char not in final_matches:
                    final_matches[required_char] = best_usage['match_info']
                    used_detected_chars.add(detected_text)
        
        # 为未匹配的字符寻找替代方案
        for required_char, match_list in matches.items():
            if required_char not in final_matches and match_list:
                # 寻找未被使用的检测字符
                for match in match_list:
                    if match['text'] not in used_detected_chars:
                        final_matches[required_char] = match
                        used_detected_chars.add(match['text'])
                        break
        
        return final_matches
    
    def get_click_coordinates(self, required_chars: List[str], 
                            detected_chars: List[Dict]) -> List[Tuple[int, int]]:
        """
        获取按顺序点击的坐标列表
        
        Args:
            required_chars: 需要点击的字符列表（按顺序）
            detected_chars: OCR检测到的字符列表
            
        Returns:
            List[Tuple[int, int]]: 点击坐标列表，按required_chars的顺序
        """
        # 找到字符匹配
        matches = self.find_character_matches(required_chars, detected_chars)
        
        # 解决冲突
        final_matches = self.resolve_conflicts(matches)
        
        # 按顺序生成坐标列表
        coordinates = []
        for required_char in required_chars:
            if required_char in final_matches:
                match = final_matches[required_char]
                coordinates.append(match['center'])
            else:
                print(f"警告: 未找到字符 '{required_char}' 的匹配")
                coordinates.append(None)
        
        return coordinates
    
    def get_detailed_results(self, required_chars: List[str], 
                           detected_chars: List[Dict]) -> Dict:
        """
        获取详细的匹配结果
        
        Args:
            required_chars: 需要点击的字符列表
            detected_chars: OCR检测到的字符列表
            
        Returns:
            Dict: 详细的匹配结果
        """
        matches = self.find_character_matches(required_chars, detected_chars)
        final_matches = self.resolve_conflicts(matches)
        coordinates = self.get_click_coordinates(required_chars, detected_chars)
        
        # 统计信息
        matched_count = len([c for c in coordinates if c is not None])
        total_count = len(required_chars)
        success_rate = matched_count / total_count if total_count > 0 else 0
        
        return {
            'required_chars': required_chars,
            'detected_chars': detected_chars,
            'matches': dict(matches),
            'final_matches': final_matches,
            'coordinates': coordinates,
            'statistics': {
                'total_required': total_count,
                'matched_count': matched_count,
                'success_rate': success_rate
            }
        }
    
    def print_matching_results(self, results: Dict):
        """
        打印匹配结果
        
        Args:
            results: get_detailed_results返回的结果
        """
        print("=== CAPTCHA字符匹配结果 ===")
        print(f"需要点击的字符: {results['required_chars']}")
        print(f"检测到的字符数量: {len(results['detected_chars'])}")
        print()
        
        print("检测到的所有字符:")
        for i, char in enumerate(results['detected_chars']):
            print(f"  {i+1}. '{char['text']}' - 坐标: {char['center']}, "
                  f"置信度: {char['confidence']:.3f}")
        print()
        
        print("字符匹配结果:")
        for i, required_char in enumerate(results['required_chars']):
            coord = results['coordinates'][i]
            if coord:
                match = results['final_matches'][required_char]
                print(f"  {i+1}. '{required_char}' -> '{match['text']}' "
                      f"坐标: {coord}, 相似度: {match['similarity']:.3f}")
            else:
                print(f"  {i+1}. '{required_char}' -> 未找到匹配")
        print()
        
        stats = results['statistics']
        print(f"匹配统计: {stats['matched_count']}/{stats['total_required']} "
              f"({stats['success_rate']:.1%})")
        
        if stats['success_rate'] == 1.0:
            print("✅ 所有字符都成功匹配!")
        else:
            print("⚠️  部分字符未能匹配，请检查OCR结果或调整参数")


def main():
    """测试函数"""
    # 模拟测试数据
    required_chars = ['日', '无', '逊', '莽']
    
    detected_chars = [
        {'text': '日', 'center': (100, 50), 'confidence': 0.95, 'bbox': [[90,40],[110,40],[110,60],[90,60]]},
        {'text': '无', 'center': (200, 50), 'confidence': 0.92, 'bbox': [[190,40],[210,40],[210,60],[190,60]]},
        {'text': '逊', 'center': (300, 50), 'confidence': 0.88, 'bbox': [[290,40],[310,40],[310,60],[290,60]]},
        {'text': '莽', 'center': (400, 50), 'confidence': 0.90, 'bbox': [[390,40],[410,40],[410,60],[390,60]]},
        {'text': '其', 'center': (150, 100), 'confidence': 0.85, 'bbox': [[140,90],[160,90],[160,110],[140,110]]},
    ]
    
    matcher = CoordinateMatcher()
    results = matcher.get_detailed_results(required_chars, detected_chars)
    matcher.print_matching_results(results)
    
    print("\n点击坐标序列:")
    for i, coord in enumerate(results['coordinates']):
        if coord:
            print(f"  第{i+1}步: 点击 {coord}")
        else:
            print(f"  第{i+1}步: 无法确定坐标")


if __name__ == "__main__":
    main()
