# CAPTCHA验证码自动解决器

这是一个基于PaddleOCR的CAPTCHA验证码自动解决器，能够解析HTML页面中的验证码，识别中文字符并返回精确的点击坐标。

## 功能特点

- 🔍 **HTML解析**: 自动从HTML页面提取CAPTCHA图像和指令文本
- 🤖 **OCR识别**: 使用PaddleOCR识别中文字符及其位置坐标
- 🎯 **智能匹配**: 将指令字符与识别字符进行智能匹配
- 📍 **坐标定位**: 返回精确的点击坐标序列
- 🔧 **可配置**: 支持GPU加速、相似度阈值调整等参数
- 📊 **可视化**: 提供调试图像和详细的匹配结果

## 安装依赖

```bash
pip install -r requirements.txt
```

### 主要依赖说明

- `paddleocr`: 百度飞桨OCR库，用于字符识别
- `pillow`: Python图像处理库
- `opencv-python`: 计算机视觉库，用于图像预处理
- `beautifulsoup4`: HTML解析库
- `numpy`: 数值计算库

## 使用方法

### 基本使用

```python
from captcha_solver import CaptchaSolver

# 创建解决器
solver = CaptchaSolver()

# 解决CAPTCHA
result = solver.solve_captcha('1.html')

# 打印结果
solver.print_solution(result)

# 获取点击坐标
coordinates = result['coordinates']
for i, coord in enumerate(coordinates):
    if coord:
        print(f"第{i+1}步: 点击坐标 {coord}")
```

### 简化接口

```python
# 只获取坐标列表
coordinates = solver.get_click_coordinates_only('1.html')
```

### 高级配置

```python
# 使用GPU加速和自定义相似度阈值
solver = CaptchaSolver(
    use_gpu=True,           # 启用GPU加速
    similarity_threshold=0.8 # 字符匹配相似度阈值
)
```

## 文件结构

```
├── captcha_solver.py      # 主程序
├── html_parser.py         # HTML解析器
├── ocr_detector.py        # OCR字符检测器
├── coordinate_matcher.py  # 坐标匹配算法
├── requirements.txt       # 依赖列表
├── README.md             # 说明文档
└── 1.html                # 示例HTML文件
```

## 工作流程

1. **HTML解析**: 解析HTML文件，提取CAPTCHA图像（base64格式）和指令文本
2. **指令解析**: 从指令文本中提取需要点击的字符序列（如"日,无,逊,莽"）
3. **图像预处理**: 对CAPTCHA图像进行增强处理，提高OCR识别准确率
4. **OCR识别**: 使用PaddleOCR识别图像中的所有字符及其边界框
5. **坐标计算**: 计算每个字符的中心点坐标
6. **字符匹配**: 将指令字符与识别字符进行相似度匹配
7. **冲突解决**: 处理多个字符匹配到同一位置的冲突情况
8. **结果输出**: 返回按顺序排列的点击坐标

## 输出格式

### 成功结果示例

```python
{
    'success': True,
    'coordinates': [(100, 50), (200, 50), (300, 50), (400, 50)],
    'required_chars': ['日', '无', '逊', '莽'],
    'click_sequence': [
        {'step': 1, 'character': '日', 'coordinate': (100, 50), 'x': 100, 'y': 50},
        {'step': 2, 'character': '无', 'coordinate': (200, 50), 'x': 200, 'y': 50},
        {'step': 3, 'character': '逊', 'coordinate': (300, 50), 'x': 300, 'y': 50},
        {'step': 4, 'character': '莽', 'coordinate': (400, 50), 'x': 400, 'y': 50}
    ],
    'statistics': {
        'total_chars': 4,
        'matched_chars': 4,
        'success_rate': 1.0
    }
}
```

## 调试功能

程序会自动生成调试图像：

- `debug_captcha_original.png`: 原始CAPTCHA图像
- `debug_ocr_result.png`: OCR识别结果可视化（包含边界框和置信度）

## 参数调优

### OCR参数

- `use_gpu`: 是否使用GPU加速（需要安装GPU版本的PaddlePaddle）
- `lang`: 语言设置，默认为'ch'（中文）

### 匹配参数

- `similarity_threshold`: 字符相似度阈值（0-1），默认0.8
  - 较高值：匹配更严格，可能遗漏相似字符
  - 较低值：匹配更宽松，可能产生误匹配

## 常见问题

### 1. OCR识别不准确

**解决方案**:
- 检查图像质量，确保字符清晰
- 调整图像预处理参数
- 尝试使用GPU加速
- 检查PaddleOCR模型是否正确安装

### 2. 字符匹配失败

**解决方案**:
- 降低`similarity_threshold`参数
- 检查指令文本解析是否正确
- 确认OCR识别的字符是否包含目标字符

### 3. 坐标偏移

**解决方案**:
- 检查HTML页面的缩放比例
- 确认坐标系统（相对于图像还是页面）
- 调整坐标偏移量

## 扩展功能

### 自定义点击操作

```python
import pyautogui

def click_coordinates(coordinates):
    """执行实际的点击操作"""
    for i, coord in enumerate(coordinates):
        if coord:
            print(f"点击第{i+1}个字符，坐标: {coord}")
            pyautogui.click(coord[0], coord[1])
            time.sleep(0.5)  # 点击间隔

# 使用示例
result = solver.solve_captcha('1.html')
if result['success']:
    click_coordinates(result['coordinates'])
```

### 批量处理

```python
import glob

def batch_solve_captcha(html_pattern):
    """批量处理CAPTCHA文件"""
    solver = CaptchaSolver()
    html_files = glob.glob(html_pattern)
    
    for html_file in html_files:
        print(f"处理文件: {html_file}")
        result = solver.solve_captcha(html_file)
        solver.print_solution(result)
        print("-" * 50)

# 使用示例
batch_solve_captcha("captcha_*.html")
```

## 许可证

本项目采用MIT许可证。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 支持中文CAPTCHA识别
- 实现坐标匹配算法
- 提供可视化调试功能
