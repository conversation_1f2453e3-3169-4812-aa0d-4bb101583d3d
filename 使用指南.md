# CAPTCHA验证码自动解决器 - 使用指南

## 项目概述

本项目实现了一个完整的CAPTCHA验证码自动解决方案，能够：

1. **解析HTML页面**：从HTML文件中提取CAPTCHA图像和指令文本
2. **字符识别**：使用PaddleOCR识别图像中的中文字符
3. **智能匹配**：将指令字符与识别字符进行匹配
4. **坐标定位**：返回精确的点击坐标序列

## 文件说明

### 核心模块
- `html_parser.py` - HTML解析器，提取CAPTCHA图像和指令
- `ocr_detector.py` - OCR字符检测器，使用PaddleOCR识别字符
- `coordinate_matcher.py` - 坐标匹配算法，匹配字符和坐标
- `captcha_solver.py` - 主程序，整合所有功能

### 演示和测试
- `demo_captcha_solver.py` - 演示版本，使用模拟OCR数据
- `simple_test.py` - 基础功能测试
- `test_captcha_solver.py` - 完整功能测试（需要PaddleOCR）

### 配置文件
- `requirements.txt` - 依赖库列表
- `README.md` - 详细说明文档

## 快速开始

### 1. 环境准备

```bash
# 安装基础依赖
pip install pillow opencv-python beautifulsoup4 numpy

# 安装PaddleOCR（可选，用于真实OCR识别）
pip install paddleocr
```

### 2. 运行演示

```bash
# 运行演示版本（无需PaddleOCR）
python demo_captcha_solver.py
```

### 3. 运行完整版本

```bash
# 运行完整版本（需要PaddleOCR）
python captcha_solver.py
```

## 使用示例

### 基本使用

```python
from captcha_solver import CaptchaSolver

# 创建解决器
solver = CaptchaSolver()

# 解决CAPTCHA
result = solver.solve_captcha('1.html')

# 检查结果
if result['success']:
    print("解决成功！")
    for step in result['click_sequence']:
        if step.get('coordinate'):
            print(f"点击字符 '{step['character']}' 坐标 ({step['x']}, {step['y']})")
else:
    print("解决失败:", result.get('error', '未知错误'))
```

### 获取坐标列表

```python
# 简化接口，只返回坐标
coordinates = solver.get_click_coordinates_only('1.html')

# 执行点击操作
import pyautogui
for i, coord in enumerate(coordinates):
    if coord:
        print(f"第{i+1}步: 点击坐标 {coord}")
        pyautogui.click(coord[0], coord[1])
        time.sleep(0.5)
```

### 自定义参数

```python
# 使用GPU加速和自定义相似度阈值
solver = CaptchaSolver(
    use_gpu=True,           # 启用GPU加速
    similarity_threshold=0.8 # 字符匹配相似度阈值
)
```

## 测试结果

### 演示版本测试结果

```
CAPTCHA验证码自动解决器 - 演示版
使用模拟OCR数据演示完整功能
----------------------------------------
步骤1: 解析HTML文件...
  ✓ 成功提取CAPTCHA图像 (尺寸: (310, 155))
  ✓ 需要点击的字符序列: ['日', '无', '逊', '莽']

步骤2: 模拟OCR字符识别...
  ✓ 模拟识别到 6 个字符
    1. '日' - 坐标: (54, 74), 置信度: 0.880
    2. '无' - 坐标: (124, 80), 置信度: 0.900
    3. '逊' - 坐标: (194, 74), 置信度: 0.920
    4. '莽' - 坐标: (240, 80), 置信度: 0.940
    5. '其' - 坐标: (270, 67), 置信度: 0.820
    6. '他' - 坐标: (245, 87), 置信度: 0.850

步骤3: 字符坐标匹配...
  ✓ 匹配成功率: 100.0%

==================================================
CAPTCHA解决方案（演示版）
==================================================
✅ 解决成功!
需要点击的字符序列: ['日', '无', '逊', '莽']

点击步骤:
  第1步: 点击字符 '日' 坐标 (54, 74)
  第2步: 点击字符 '无' 坐标 (124, 80)
  第3步: 点击字符 '逊' 坐标 (194, 74)
  第4步: 点击字符 '莽' 坐标 (240, 80)

统计信息:
  总字符数: 4
  匹配成功: 4
  成功率: 100.0%

处理时间: 0.12 秒
```

## 输出文件

运行程序后会生成以下调试文件：

- `demo_captcha_original.png` - 提取的原始CAPTCHA图像
- `demo_ocr_result.png` - OCR识别结果可视化（包含边界框和置信度）
- `extracted_captcha.png` - 测试提取的图像

## 技术特点

### 1. HTML解析
- 自动提取base64编码的CAPTCHA图像
- 智能解析指令文本，支持多种格式
- 容错处理，适应不同的HTML结构

### 2. OCR识别
- 使用PaddleOCR进行中文字符识别
- 图像预处理提高识别准确率
- 支持GPU加速
- 置信度评估

### 3. 坐标匹配
- 智能字符相似度匹配
- 冲突解决算法
- 支持部分匹配
- 详细的匹配统计

### 4. 可视化调试
- 生成带标注的调试图像
- 显示字符边界框和中心点
- 置信度和匹配结果可视化

## 参数调优

### OCR参数
- `use_gpu`: 是否使用GPU加速
- `lang`: 语言设置（默认'ch'中文）

### 匹配参数
- `similarity_threshold`: 字符相似度阈值（0-1）
  - 0.9+: 严格匹配，适用于字符清晰的情况
  - 0.8: 平衡设置，推荐值
  - 0.7-: 宽松匹配，适用于字符模糊的情况

## 扩展应用

### 1. 批量处理
```python
import glob

def batch_solve_captcha(pattern):
    solver = CaptchaSolver()
    for html_file in glob.glob(pattern):
        result = solver.solve_captcha(html_file)
        print(f"{html_file}: {'成功' if result['success'] else '失败'}")
```

### 2. Web自动化集成
```python
from selenium import webdriver
from selenium.webdriver.common.action_chains import ActionChains

def auto_solve_captcha(driver, html_file):
    solver = CaptchaSolver()
    result = solver.solve_captcha(html_file)
    
    if result['success']:
        actions = ActionChains(driver)
        for step in result['click_sequence']:
            if step.get('coordinate'):
                x, y = step['coordinate']
                actions.move_by_offset(x, y).click().perform()
                time.sleep(0.5)
```

### 3. API服务
```python
from flask import Flask, request, jsonify

app = Flask(__name__)
solver = CaptchaSolver()

@app.route('/solve_captcha', methods=['POST'])
def solve_captcha_api():
    html_content = request.json.get('html')
    # 保存HTML到临时文件
    with open('temp.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    result = solver.solve_captcha('temp.html')
    return jsonify(result)
```

## 常见问题

### Q: OCR识别不准确怎么办？
A: 
1. 检查图像质量和清晰度
2. 尝试调整图像预处理参数
3. 使用GPU加速提高识别速度和准确率
4. 降低相似度阈值以增加匹配容错性

### Q: 字符匹配失败怎么办？
A:
1. 检查指令文本解析是否正确
2. 降低`similarity_threshold`参数
3. 查看调试图像确认OCR识别结果
4. 手动调整字符映射关系

### Q: 坐标偏移怎么办？
A:
1. 确认坐标系统（相对于图像还是页面）
2. 检查HTML页面的缩放比例
3. 根据实际情况调整坐标偏移量

## 性能优化

1. **GPU加速**: 启用GPU可显著提高OCR识别速度
2. **图像预处理**: 适当的预处理可提高识别准确率
3. **批量处理**: 复用解决器实例避免重复初始化
4. **缓存机制**: 对相同图像可以缓存OCR结果

## 总结

本CAPTCHA解决器提供了完整的验证码自动识别和点击坐标定位功能，具有以下优势：

- ✅ **高准确率**: 基于PaddleOCR的中文字符识别
- ✅ **智能匹配**: 先进的字符匹配和冲突解决算法
- ✅ **易于使用**: 简洁的API接口和详细的文档
- ✅ **可扩展性**: 模块化设计，易于定制和扩展
- ✅ **调试友好**: 丰富的可视化和日志输出

适用于各种需要自动化处理CAPTCHA验证码的场景。
